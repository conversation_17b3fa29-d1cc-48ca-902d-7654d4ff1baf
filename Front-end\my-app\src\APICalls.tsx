import React, { useState, useEffect } from 'react';

export function APICalls() {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  interface User {
    id: number;
    name: string;
    email: string;
  }

  async function fetchUsers(): Promise<User[]> {
    const response = await fetch('/api/users');
    const data = await response.json();
    return data;
  }
}