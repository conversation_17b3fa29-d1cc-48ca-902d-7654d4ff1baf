import React, { useState, useEffect } from "react";
import { fetchUsers } from "./APICalls";


function Afficher() {  
  const [users, setUsers] = useState<User>([]);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    async function fetchData() {
      try {
        const data = await fetchUsers();
        setUsers(data);
      } catch (error) {
        setError(error.message);
      }
    }
    fetchData();
  }, []);
  if (error) {
    return <div>Error: {error}</div>;
  }
  return (
    <div>
      {users.map((user) => (
        <div key={user.id}>
          <h2>{user.name}</h2>
          <p>{user.email}</p>
        </div>
      ))}
    </div>
  );
}

export default Afficher;